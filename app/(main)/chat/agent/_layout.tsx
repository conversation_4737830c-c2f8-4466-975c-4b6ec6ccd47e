import { HapticTab } from '@/components/HapticTab'
import colors, { primary } from '@/styles/_colors'
import { getDynamicTabBarHeight } from '@/styles/_variables'
import { Tabs } from 'expo-router'
import React from 'react'
import { Platform } from 'react-native'

import IconTest from '@/assets/icons/3d-cube-scan-icon.svg'
function ChatAgentLayout() {
  return (
    <Tabs
      initialRouteName="inbox"
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: primary[500],
        tabBarInactiveTintColor: '#8B8C99',
        tabBarStyle: {
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -1.2 },
              shadowOpacity: 0.1,
              shadowRadius: 1.2,
              borderTopWidth: 0,
            },
            android: {
              elevation: 8,
              borderTopWidth: 0.5,
              borderTopColor: colors.divider?.default || '#E5E5E5',
            },
          }),
          height: getDynamicTabBarHeight(),
          paddingBottom: Platform.OS === 'ios' ? 20 : 10,
          backgroundColor: 'white',
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Inter',
          fontWeight: '400',
          marginTop: 6,
        },
        tabBarItemStyle: {
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        },
        tabBarButton: (props) => <HapticTab {...props} />,
      }}
    >
      <Tabs.Screen
        name="inbox"
        options={{
          title: 'Inbox',
          tabBarIcon: ({ focused, color }) => <IconTest />,
        }}
      />

      <Tabs.Screen
        name="conversation"
        options={{
          title: 'Conversation',
          tabBarIcon: ({ focused, color }) => <IconTest />,
        }}
      />
    </Tabs>
  )
}

export default ChatAgentLayout
